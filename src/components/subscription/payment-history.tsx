"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ExternalLinkIcon,
  RefreshCwIcon,
  Receipt,
  Trash2Icon,
} from "lucide-react";
import { PaymentStatus } from "@prisma/client";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";
import PaymentDetailModal from "./payment-detail-modal";

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string | null;
  externalUrl: string | null;
  paymentDate: Date | null;
  expiryDate: Date | null;
  createdAt: Date;
  subscription: {
    id: string;
    plan: string;
  } | null;
}

export default function PaymentHistory() {
  const router = useRouter();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshingId, setRefreshingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [showPaymentDetail, setShowPaymentDetail] = useState(false);

  // Format currency
  const formatCurrency = (amount: number, currency = "IDR") => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date | null) => {
    if (!date) return "-";
    return format(new Date(date), "dd MMMM yyyy, HH:mm", { locale: id });
  };

  // Get payment status badge
  const getStatusBadge = (status: PaymentStatus) => {
    switch (status) {
      case "COMPLETED":
        return (
          <div className="flex items-center gap-1.5">
            <div className="h-2 w-2 rounded-full bg-green-500"></div>
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200"
            >
              Lunas
            </Badge>
          </div>
        );
      case "PENDING":
        return (
          <div className="flex items-center gap-1.5">
            <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
            <Badge
              variant="outline"
              className="bg-yellow-50 text-yellow-700 border-yellow-200"
            >
              Menunggu Pembayaran
            </Badge>
          </div>
        );
      case "FAILED":
        return (
          <div className="flex items-center gap-1.5">
            <div className="h-2 w-2 rounded-full bg-red-500"></div>
            <Badge
              variant="outline"
              className="bg-red-50 text-red-700 border-red-200"
            >
              Gagal
            </Badge>
          </div>
        );
      case "EXPIRED":
        return (
          <div className="flex items-center gap-1.5">
            <div className="h-2 w-2 rounded-full bg-gray-400"></div>
            <Badge
              variant="outline"
              className="bg-gray-50 text-gray-700 border-gray-200"
            >
              Kedaluwarsa
            </Badge>
          </div>
        );
      case "REFUNDED":
        return (
          <div className="flex items-center gap-1.5">
            <div className="h-2 w-2 rounded-full bg-blue-500"></div>
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200"
            >
              Dikembalikan
            </Badge>
          </div>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Fetch payment history
  const fetchPayments = async () => {
    try {
      console.log("📋 [PAYMENT HISTORY] Fetching payment history");
      setIsLoading(true);
      const response = await fetch("/api/payments");

      if (!response.ok) {
        console.error(
          "❌ [PAYMENT HISTORY] Failed to fetch payments:",
          response.status
        );
        throw new Error("Failed to fetch payments");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment history loaded:", {
        count: data.payments?.length || 0,
      });
      setPayments(data.payments || []);
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error fetching payments:", error);
      toast.error("Gagal memuat riwayat pembayaran");
    } finally {
      setIsLoading(false);
    }
  };

  // Check payment status
  const checkPaymentStatus = async (paymentId: string) => {
    try {
      console.log("🔍 [PAYMENT HISTORY] Starting payment status check:", {
        paymentId,
      });
      setRefreshingId(paymentId);

      const response = await fetch(`/api/payments/check/${paymentId}`, {
        method: "POST",
      });

      console.log("📊 [PAYMENT HISTORY] Payment status check response:", {
        paymentId,
        status: response.status,
        ok: response.ok,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ [PAYMENT HISTORY] Payment status check failed:", {
          paymentId,
          status: response.status,
          error: errorData.error,
        });
        throw new Error(errorData.error || "Failed to check payment status");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment status check successful:", {
        paymentId,
        newStatus: data.status,
        hasStatusChange: !!data.status,
      });

      // If status changed, refresh the list
      if (data.status) {
        console.log(
          "🔄 [PAYMENT HISTORY] Status changed, refreshing payment list:",
          {
            paymentId,
            newStatus: data.status,
          }
        );
        fetchPayments();

        // If payment is completed, refresh the page to update subscription status
        if (data.status === "COMPLETED") {
          console.log(
            "💰 [PAYMENT HISTORY] Payment completed, refreshing page:",
            { paymentId }
          );
          toast.success("Pembayaran berhasil dikonfirmasi!");
          router.refresh();
        } else {
          toast.success(`Status pembayaran diperbarui: ${data.status}`);
        }
      } else {
        console.log("ℹ️ [PAYMENT HISTORY] No status change detected:", {
          paymentId,
        });
        toast.info("Status pembayaran tidak berubah");
      }
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error checking payment status:", {
        paymentId,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });
      toast.error(
        error instanceof Error
          ? error.message
          : "Gagal memeriksa status pembayaran"
      );
    } finally {
      console.log("🏁 [PAYMENT HISTORY] Payment status check completed:", {
        paymentId,
      });
      setRefreshingId(null);
    }
  };

  // Delete payment
  const deletePayment = async (paymentId: string) => {
    try {
      console.log("🗑️ [PAYMENT HISTORY] Starting payment deletion:", {
        paymentId,
      });
      setDeletingId(paymentId);

      const response = await fetch(`/api/payments?id=${paymentId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ [PAYMENT HISTORY] Failed to delete payment:", {
          paymentId,
          status: response.status,
          error: errorData.error,
        });
        throw new Error(errorData.error || "Failed to delete payment");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment deleted successfully:", {
        paymentId,
        message: data.message,
      });

      // Remove the payment from the local state
      setPayments((prev) => prev.filter((payment) => payment.id !== paymentId));

      // Clear confirmation state
      setDeleteConfirmId(null);

      toast.success("Pembayaran berhasil dihapus");
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error deleting payment:", {
        paymentId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      toast.error(
        error instanceof Error ? error.message : "Gagal menghapus pembayaran"
      );
    } finally {
      setDeletingId(null);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = (paymentId: string) => {
    console.log("🔔 [PAYMENT HISTORY] Delete confirmation requested:", {
      paymentId,
    });
    setDeleteConfirmId(paymentId);
  };

  // Handle delete cancel
  const handleDeleteCancel = () => {
    console.log("❌ [PAYMENT HISTORY] Delete confirmation cancelled");
    setDeleteConfirmId(null);
  };

  // Handle order ID click to show payment details
  const handleOrderIdClick = (payment: Payment) => {
    console.log("🔍 [PAYMENT HISTORY] Order ID clicked:", {
      paymentId: payment.id,
      orderId: payment.invoiceId,
    });
    setSelectedPayment(payment);
    setShowPaymentDetail(true);
  };

  // Load payments on component mount
  useEffect(() => {
    fetchPayments();
  }, []);

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold">
              Riwayat Pembayaran
            </CardTitle>
            <CardDescription className="mt-1">
              Daftar semua pembayaran langganan Anda
            </CardDescription>
          </div>
          {payments.length > 0 && (
            <div className="text-sm text-muted-foreground">
              Total: {payments.length} transaksi
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="px-0">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="flex flex-col items-center gap-2">
              <svg
                className="animate-spin h-8 w-8 text-primary"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-sm text-muted-foreground">
                Memuat riwayat pembayaran...
              </p>
            </div>
          </div>
        ) : payments.length === 0 ? (
          <div className="text-center py-12 px-4">
            <div className="flex flex-col items-center gap-3">
              <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                <Receipt className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium">Belum Ada Pembayaran</h3>
              <p className="text-sm text-muted-foreground max-w-md">
                Riwayat pembayaran Anda akan muncul di sini setelah Anda
                melakukan pembayaran untuk langganan.
              </p>
            </div>
          </div>
        ) : (
          <div className="border rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Tanggal
                    </TableHead>
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Order ID
                    </TableHead>
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Paket
                    </TableHead>
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Jumlah
                    </TableHead>
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Status
                    </TableHead>
                    <TableHead className="font-semibold text-foreground px-6 py-4">
                      Tanggal Bayar
                    </TableHead>
                    <TableHead className="font-semibold text-foreground text-right px-6 py-4">
                      Aksi
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => {
                    const planName = payment.subscription
                      ? SUBSCRIPTION_PLANS[
                          payment.subscription
                            .plan as keyof typeof SUBSCRIPTION_PLANS
                        ]?.name
                      : "Unknown";

                    return (
                      <TableRow
                        key={payment.id}
                        className="hover:bg-muted/30 transition-colors"
                      >
                        <TableCell className="px-6 py-4 font-medium">
                          <div className="text-sm">
                            {formatDate(payment.createdAt)}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="text-sm">
                            {payment.invoiceId ? (
                              <button
                                className="text-blue-600 hover:text-blue-800 hover:underline font-mono text-xs"
                                onClick={() => handleOrderIdClick(payment)}
                                title="Klik untuk melihat detail pembayaran"
                              >
                                {payment.invoiceId}
                              </button>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="font-medium text-sm">{planName}</div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="font-semibold text-sm">
                            {formatCurrency(
                              Number(payment.amount),
                              payment.currency
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          {getStatusBadge(payment.status)}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="text-sm text-muted-foreground">
                            {formatDate(payment.paymentDate)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right px-6 py-4">
                          <div className="flex justify-end gap-2 items-center">
                            {payment.status === "PENDING" && (
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-full"
                                onClick={() => checkPaymentStatus(payment.id)}
                                disabled={refreshingId === payment.id}
                                title="Periksa Status Pembayaran"
                              >
                                {refreshingId === payment.id ? (
                                  <RefreshCwIcon className="h-4 w-4 animate-spin" />
                                ) : (
                                  <RefreshCwIcon className="h-4 w-4" />
                                )}
                              </Button>
                            )}

                            {payment.externalUrl &&
                              payment.status === "PENDING" && (
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="rounded-full"
                                  onClick={() =>
                                    window.open(payment.externalUrl!, "_blank")
                                  }
                                >
                                  <ExternalLinkIcon className="h-4 w-4 mr-1.5" />
                                  Bayar
                                </Button>
                              )}

                            {/* Delete button - only show for non-completed payments */}
                            {payment.status !== "COMPLETED" && (
                              <>
                                {deleteConfirmId === payment.id ? (
                                  <div className="flex gap-1">
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      className="rounded-full text-xs px-2"
                                      onClick={() => deletePayment(payment.id)}
                                      disabled={deletingId === payment.id}
                                    >
                                      {deletingId === payment.id ? (
                                        <RefreshCwIcon className="h-3 w-3 animate-spin mr-1" />
                                      ) : null}
                                      Ya
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="rounded-full text-xs px-2"
                                      onClick={handleDeleteCancel}
                                      disabled={deletingId === payment.id}
                                    >
                                      Batal
                                    </Button>
                                  </div>
                                ) : (
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="h-8 w-8 rounded-full text-red-600 hover:text-red-700 hover:bg-red-50"
                                    onClick={() =>
                                      handleDeleteConfirm(payment.id)
                                    }
                                    disabled={deletingId === payment.id}
                                    title="Hapus Pembayaran"
                                  >
                                    <Trash2Icon className="h-4 w-4" />
                                  </Button>
                                )}
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>

      {/* Payment Detail Modal */}
      <PaymentDetailModal
        payment={selectedPayment}
        isOpen={showPaymentDetail}
        onClose={() => {
          setShowPaymentDetail(false);
          setSelectedPayment(null);
        }}
      />
    </Card>
  );
}
