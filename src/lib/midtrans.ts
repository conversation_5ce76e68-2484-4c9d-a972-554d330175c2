const midtransClient = require("midtrans-client");
import { getUserCompanyId } from "@/actions/users/company-id";
import { db } from "@/lib/prisma";

// Initialize Midtrans Snap client with configuration from environment variables
const snap = new midtransClient.Snap({
  isProduction: process.env.MIDTRANS_IS_PRODUCTION === "true",
  serverKey: process.env.MIDTRANS_SERVER_KEY || "",
  clientKey: process.env.MIDTRANS_CLIENT_KEY || "",
});

// Initialize Midtrans Core API client for transaction status checking
const coreApi = new midtransClient.CoreApi({
  isProduction: process.env.MIDTRANS_IS_PRODUCTION === "true",
  serverKey: process.env.MIDTRANS_SERVER_KEY || "",
  clientKey: process.env.MIDTRANS_CLIENT_KEY || "",
});

// Export Midtrans clients
export { snap, coreApi };

// Helper function to generate readable order ID in format: {plan_name}-{company_id}-{date}-{incremental_number}
async function generateReadableOrderId(
  userId: string,
  planName: string
): Promise<string> {
  console.log("🏷️ [MIDTRANS] Generating readable order ID:", {
    userId,
    planName,
  });

  try {
    // Get user's company ID
    const companyId = await getUserCompanyId(userId);

    // Generate date component in Jakarta timezone
    const now = new Date();
    const jakartaTime = new Date(now.getTime() + 7 * 60 * 60 * 1000); // UTC+7 for Jakarta time
    const dateStr = jakartaTime.toISOString().slice(0, 10).replace(/-/g, ""); // YYYYMMDD format

    console.log("📅 [MIDTRANS] Jakarta date calculated:", {
      originalTime: now.toISOString(),
      jakartaTime: jakartaTime.toISOString(),
      dateStr,
    });

    // Map plan names to proper format
    const planNameMap: { [key: string]: string } = {
      BASIC: "PaketBasic",
      PRO: "PaketPro",
      ENTERPRISE: "PaketEnterprise",
      basic: "PaketBasic",
      pro: "PaketPro",
      enterprise: "PaketEnterprise",
    };

    const formattedPlanName = planNameMap[planName] || `Paket${planName}`;

    // Get or create order counter for this user/company/plan/date combination
    const orderCounter = await db.orderCounter.upsert({
      where: {
        userId_companyId_planName_date: {
          userId,
          companyId: companyId || null,
          planName: formattedPlanName,
          date: dateStr,
        },
      },
      update: {
        counter: {
          increment: 1,
        },
      },
      create: {
        userId,
        companyId: companyId || null,
        planName: formattedPlanName,
        date: dateStr,
        counter: 1,
      },
    });

    console.log("🔢 [MIDTRANS] Order counter retrieved/created:", {
      orderCounterId: orderCounter.id,
      counter: orderCounter.counter,
      userId,
      companyId,
      planName: formattedPlanName,
      date: dateStr,
    });

    if (companyId) {
      const orderId = `${formattedPlanName}-${companyId}-${dateStr}-${orderCounter.counter}`;
      console.log("✅ [MIDTRANS] Generated order ID with company ID:", {
        orderId,
        companyId,
        planName: formattedPlanName,
        dateStr,
        counter: orderCounter.counter,
      });
      return orderId;
    } else {
      // Fallback to NOCOMPANY if no company ID available
      const fallbackOrderId = `${formattedPlanName}-NOCOMPANY-${dateStr}-${orderCounter.counter}`;

      console.log(
        "⚠️ [MIDTRANS] No company ID found, using fallback order ID:",
        {
          fallbackOrderId,
          userId,
          planName: formattedPlanName,
          dateStr,
          counter: orderCounter.counter,
        }
      );
      return fallbackOrderId;
    }
  } catch (error) {
    console.error("❌ [MIDTRANS] Error generating order ID:", {
      userId,
      planName,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    // Fallback to timestamp-based ID on error
    const now = new Date();
    const jakartaTime = new Date(now.getTime() + 7 * 60 * 60 * 1000);
    const timeStr = jakartaTime.toISOString().slice(11, 19).replace(/:/g, "");
    const randomSuffix = Math.random()
      .toString(36)
      .substring(2, 8)
      .toUpperCase();

    const planNameMap: { [key: string]: string } = {
      BASIC: "PaketBasic",
      PRO: "PaketPro",
      ENTERPRISE: "PaketEnterprise",
      basic: "PaketBasic",
      pro: "PaketPro",
      enterprise: "PaketEnterprise",
    };
    const formattedPlanName = planNameMap[planName] || `Paket${planName}`;
    const fallbackOrderId = `${formattedPlanName}-ERROR-${timeStr}-${randomSuffix}`;

    console.log("🔄 [MIDTRANS] Using fallback order ID due to error:", {
      fallbackOrderId,
    });
    return fallbackOrderId;
  }
}

// Helper function to get comprehensive user details for transaction
async function getUserDetailsForTransaction(userId: string) {
  console.log("👤 [MIDTRANS] Fetching user details for transaction:", {
    userId,
  });

  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        additionalInfo: true,
      },
    });

    if (!user) {
      console.log("❌ [MIDTRANS] User not found:", { userId });
      return null;
    }

    const userDetails = {
      // Basic user info
      first_name: user.name?.split(" ")[0] || "Customer",
      last_name: user.name?.split(" ").slice(1).join(" ") || "",
      email: user.email || "",
      phone: user.phone || user.additionalInfo?.companyPhone || "",

      // Company/Additional info
      company_name: user.additionalInfo?.companyName || "",
      company_id: user.additionalInfo?.companyId || "",
      company_address: user.additionalInfo?.companyAddress || "",
      company_phone: user.additionalInfo?.companyPhone || "",
      company_email: user.additionalInfo?.companyEmail || user.email || "",
      position: user.additionalInfo?.position || "",
      industry: user.additionalInfo?.industry || "",
      city: user.additionalInfo?.city || "",
      postal_code: user.additionalInfo?.postalCode || "",
    };

    console.log("✅ [MIDTRANS] User details retrieved:", {
      userId,
      hasName: !!userDetails.first_name,
      hasEmail: !!userDetails.email,
      hasPhone: !!userDetails.phone,
      hasCompanyInfo: !!userDetails.company_name,
      companyId: userDetails.company_id,
    });

    return userDetails;
  } catch (error) {
    console.error("❌ [MIDTRANS] Error fetching user details:", {
      userId,
      error: error instanceof Error ? error.message : "Unknown error",
    });
    return null;
  }
}

// Helper function to create a Midtrans transaction
export async function createTransaction({
  orderId,
  amount,
  description,
  customer,
  items,
  successRedirectUrl,
  failureRedirectUrl,
  userId,
  planName,
}: {
  orderId: string;
  amount: number;
  description: string;
  customer?: {
    email?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
  };
  items?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    category?: string;
  }>;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
  userId?: string;
  planName?: string;
}) {
  // Generate more readable order ID if userId and planName are provided
  const readableOrderId =
    userId && planName
      ? await generateReadableOrderId(userId, planName)
      : orderId;

  // Get comprehensive user details if userId is provided.
  // The quality of the checkout page depends on the completeness of this data.
  const userDetails = userId
    ? await getUserDetailsForTransaction(userId)
    : null;

  console.log("✅ [DEBUG] Fetched User Details for Midtrans:", userDetails);

  // Use comprehensive user details or fallback to provided customer data
  const enhancedCustomer = userDetails
    ? {
        first_name: userDetails.first_name,
        last_name: userDetails.last_name,
        email: userDetails.email,
        phone: userDetails.phone,
        company_name: userDetails.company_name,
        company_address: userDetails.company_address,
      }
    : customer;

  console.log("🚀 [MIDTRANS] Creating transaction:", {
    readableOrderId,
    amount,
    hasUserDetails: !!userDetails,
  });

  try {
    const SUBSCRIPTION_PLANS = {
      BASIC: { name: "Paket Basic" },
      PRO: { name: "Paket Pro" },
      ENTERPRISE: { name: "Paket Enterprise" },
    };

    const enhancedItems = items || [
      {
        id: planName ? `plan-${planName.toLowerCase()}` : "subscription",
        name: planName
          ? `Langganan ${
              SUBSCRIPTION_PLANS[planName as keyof typeof SUBSCRIPTION_PLANS]
                ?.name || planName
            }`
          : description,
        price: amount,
        quantity: 1,
        category: "Subscription Plan",
      },
    ];

    // Correctly separate the street address from other components for clean display.
    const streetAddress = userDetails?.company_address || "";

    // Prepare the final transaction parameter object to be sent to Midtrans.
    const parameter = {
      transaction_details: {
        order_id: readableOrderId,
        gross_amount: amount,
        // Custom fields are for your internal use and appear in the dashboard/webhooks,
        // not on the customer checkout page.
        custom_field1: `Plan: ${planName}`,
        custom_field2: `User ID: ${userId}`,
        custom_field3: `Company ID: ${userDetails?.company_id || "N/A"}`,
      },
      item_details: enhancedItems,
      customer_details: enhancedCustomer
        ? {
            first_name: enhancedCustomer.first_name || "",
            last_name: enhancedCustomer.last_name || "",
            email: enhancedCustomer.email || "",
            phone: enhancedCustomer.phone || "",
            billing_address: {
              first_name: enhancedCustomer.first_name || "",
              last_name: enhancedCustomer.last_name || "",
              email: enhancedCustomer.email || "",
              phone: enhancedCustomer.phone || "",
              address: streetAddress,
              city: userDetails?.city || "",
              postal_code: userDetails?.postal_code || "",
              country_code: "IDN",
            },
            shipping_address: {
              first_name: enhancedCustomer.first_name || "",
              last_name: enhancedCustomer.last_name || "",
              email: enhancedCustomer.email || "",
              phone: enhancedCustomer.phone || "",
              address: streetAddress,
              city: userDetails?.city || "",
              postal_code: userDetails?.postal_code || "",
              country_code: "IDN",
            },
          }
        : undefined,
      callbacks: {
        finish:
          successRedirectUrl ||
          `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/success`,
        error:
          failureRedirectUrl ||
          `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/failed`,
        pending: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/pending`,
      },
      credit_card: {
        secure: true,
      },
      expiry: {
        start_time: (() => {
          const now = new Date();
          const jakartaTime = new Date(now.getTime() + 7 * 60 * 60 * 1000); // UTC+7
          const year = jakartaTime.getUTCFullYear();
          const month = String(jakartaTime.getUTCMonth() + 1).padStart(2, "0");
          const day = String(jakartaTime.getUTCDate()).padStart(2, "0");
          const hours = String(jakartaTime.getUTCHours()).padStart(2, "0");
          const minutes = String(jakartaTime.getUTCMinutes()).padStart(2, "0");
          const seconds = String(jakartaTime.getUTCSeconds()).padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} +0700`;
        })(),
        unit: "hours",
        duration: 24,
      },
    };

    console.log("📝 [MIDTRANS] Transaction parameter prepared.");

    // Create transaction using Snap API
    const transaction = await snap.createTransaction(parameter);

    console.log("✅ [MIDTRANS] Transaction created successfully.");

    return {
      success: true,
      data: {
        token: transaction.token,
        redirect_url: transaction.redirect_url,
        order_id: readableOrderId,
        original_order_id: orderId,
      },
    };
  } catch (error) {
    console.error("❌ [MIDTRANS] Error creating transaction:", {
      orderId,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      details: error,
    };
  }
}

// Helper function to get transaction status
export async function getTransactionStatus(orderId: string) {
  console.log("🔍 [MIDTRANS] Checking transaction status:", { orderId });

  try {
    const statusResponse = await coreApi.transaction.status(orderId);

    console.log("✅ [MIDTRANS] Transaction status retrieved:", {
      orderId,
      transaction_status: statusResponse.transaction_status,
    });

    return {
      success: true,
      data: statusResponse,
    };
  } catch (error) {
    console.error("❌ [MIDTRANS] Error getting transaction status:", {
      orderId,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

// Helper function to verify notification signature
export function verifyNotificationSignature(
  orderId: string,
  statusCode: string,
  grossAmount: string,
  signatureKey: string
): boolean {
  console.log("🔐 [MIDTRANS] Verifying notification signature:", {
    orderId,
  });

  try {
    const crypto = require("crypto");
    const serverKey = process.env.MIDTRANS_SERVER_KEY || "";

    const signatureString = orderId + statusCode + grossAmount + serverKey;
    const calculatedSignature = crypto
      .createHash("sha512")
      .update(signatureString)
      .digest("hex");

    const isValid = calculatedSignature === signatureKey;

    console.log("🔐 [MIDTRANS] Signature verification result:", {
      orderId,
      isValid,
    });

    return isValid;
  } catch (error) {
    console.error("❌ [MIDTRANS] Error verifying signature:", {
      orderId,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return false;
  }
}

// Helper function to handle notification
export async function handleNotification(notificationBody: any) {
  console.log("📨 [MIDTRANS] Processing notification:", {
    order_id: notificationBody.order_id,
    transaction_status: notificationBody.transaction_status,
  });

  try {
    const isValidSignature = verifyNotificationSignature(
      notificationBody.order_id,
      notificationBody.status_code,
      notificationBody.gross_amount,
      notificationBody.signature_key
    );

    if (!isValidSignature) {
      console.error("❌ [MIDTRANS] Invalid notification signature:", {
        order_id: notificationBody.order_id,
      });

      return {
        success: false,
        error: "Invalid signature",
      };
    }

    const statusResult = await getTransactionStatus(notificationBody.order_id);

    if (!statusResult.success) {
      console.error("❌ [MIDTRANS] Failed to verify transaction status:", {
        order_id: notificationBody.order_id,
        error: statusResult.error,
      });

      return {
        success: false,
        error: "Failed to verify transaction status",
      };
    }

    console.log("✅ [MIDTRANS] Notification processed successfully:", {
      order_id: notificationBody.order_id,
      verified_status: statusResult.data.transaction_status,
    });

    return {
      success: true,
      data: statusResult.data,
    };
  } catch (error) {
    console.error("❌ [MIDTRANS] Error processing notification:", {
      order_id: notificationBody.order_id,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

// Helper function to map Midtrans status to application status
export function mapMidtransStatus(
  transactionStatus: string,
  fraudStatus?: string
): string {
  console.log("🔄 [MIDTRANS] Mapping transaction status:", {
    transactionStatus,
    fraudStatus,
  });

  let mappedStatus: string;

  switch (transactionStatus) {
    case "capture":
      if (fraudStatus === "accept") {
        mappedStatus = "COMPLETED";
      } else if (fraudStatus === "challenge") {
        mappedStatus = "PENDING";
      } else {
        mappedStatus = "FAILED";
      }
      break;
    case "settlement":
      mappedStatus = "COMPLETED";
      break;
    case "pending":
      mappedStatus = "PENDING";
      break;
    case "deny":
    case "cancel":
    case "expire":
    case "failure":
      mappedStatus = "FAILED";
      break;
    default:
      mappedStatus = "PENDING";
  }

  console.log("🔄 [MIDTRANS] Status mapped:", {
    from: transactionStatus,
    to: mappedStatus,
  });

  return mappedStatus;
}
